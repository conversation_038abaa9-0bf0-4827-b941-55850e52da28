import { Inject, Injectable, Logger } from '@nestjs/common';
import * as portal from '@EmmySoft-GmbH/emmysoft-client';
import { TenantService } from '@core/tenant/tenant-service.interface';
import { TENANT_SERVICE } from '@core/constants';
import { PORTAL_V4_SERVICE, PortalV4Service } from '@core/portal-v4.service.interface';
import { JobApplicationEntity } from './entity';
import { JobApplicationDetails } from './entity/job-application.details';
import { RefEntity, ReferenceUtil } from '@core/reference.util';
import { UpsertJobApplicationInput } from '@core/portal-v4.types';
import { ScoreDetails } from '@core/job-application/entity/score.details';
import { sanitizeEmmyId } from '@core/emmy-id.sanitizer';
import { Reference } from '@EmmySoft-GmbH/emmysoft-client';
import { constructStringReferenceValue } from '@EmmySoft-GmbH/emmysoft-client/src/portal/util/reference.util';

@Injectable()
export class NewJobApplicationsService {
  private readonly logger = new Logger(NewJobApplicationsService.name);

  constructor(
    @Inject(PORTAL_V4_SERVICE) private readonly portalService: PortalV4Service,
    @Inject(TENANT_SERVICE) private readonly tenantService: TenantService,
  ) {}

  async upsertChecksum(jobApplicationEntity: JobApplicationEntity): Promise<void> {
    const tenantConfig = this.tenantService.getDefaultTenantConfig();
    const context: portal.Context = { tenantId: tenantConfig.id };

    const emmyId = sanitizeEmmyId(jobApplicationEntity.emmyId);

    const ref = ReferenceUtil.withCompositeId(
      RefEntity.JOB_APPLICATION,
      jobApplicationEntity.source,
      jobApplicationEntity.rowId,
    );
    ReferenceUtil.addChecksum(ref, jobApplicationEntity.checksum);
    //ReferenceUtil.addId(ref, RefEntity.JOB_APPLICATION, jobApplicationEntity.rowId);
    this.logger.debug('Finding application by emmyId: %s', emmyId);
    const existingApplication = await this.portalService.getJobApplication(context, emmyId);
    if (existingApplication) {
      const jobApplicationDto: Partial<UpsertJobApplicationInput> = {
        reference: {
          ...existingApplication.reference,
          ...ref.toRecord(),
        },
      };
      this.logger.debug('Updating application reference: %s:%s', ref.key, ref.value);
      await this.portalService.updateJobApplication(context, existingApplication.id, jobApplicationDto);

      await this.updateJobApplicationDocuments(context, existingApplication, jobApplicationEntity);
    }
  }

  private async updateJobApplicationDocuments(
    context: portal.Context,
    existingApplication: any,
    jobApplicationEntity: JobApplicationEntity,
  ): Promise<void> {
    try {
      if (!existingApplication.documentReferences || existingApplication.documentReferences.length === 0) {
        this.logger.debug('No document references found for job application: %s', existingApplication.id);
        return;
      }

      this.logger.debug(
        'Found %d document references to update with meffertId: %s',
        existingApplication.documentReferences.length,
        existingApplication.id,
      );

      const ref = new Reference('meffertJobApplicationId', jobApplicationEntity.rowId);

      for (const documentId of existingApplication.documentReferences) {
        try {
          const document = await this.portalService.getDocumentById(context, documentId);

          const updatedReference = {
            ...document.reference,
            ...ref.toRecord(),
          };

          await this.portalService.updateDocument(context, documentId, {
            reference: updatedReference,
          });

          this.logger.debug('Updated document %s with meffertId: %s', documentId, existingApplication.id);
        } catch (docError) {
          this.logger.warn('Failed to update document %s: %s', documentId, docError.message);
        }
      }
    } catch (error) {
      this.logger.error('Error updating job application documents: %s', error.message, error);
    }
  }

  // JobApplicationDto
  async jobApplications(): Promise<JobApplicationEntity[]> {
    const tenantConfig = this.tenantService.getDefaultTenantConfig();
    const context: portal.Context = { tenantId: tenantConfig.id };
    const data: JobApplicationEntity[] = [];
    const applications = await this.portalService.getNewJobApplications(context, 1, 100);

    for (let i = 0; i < applications.data.length; i++) {
      const application = applications.data[i];
      const rowId = application.additionalData.meffert?.jobApplication?.rowId;
      const jobId = application.projectAdditionalData?.meffert?.job?.id;
      const candidateId = application.candidateAdditionalData?.meffert?.candidate?.rowId;
      const app_entity = new JobApplicationEntity();
      app_entity.rowId = rowId;
      app_entity.emmyId = application.id;
      app_entity.jobApplication = new JobApplicationDetails();
      app_entity.jobApplication.id = rowId;
      app_entity.jobApplication.candidateId = candidateId;
      app_entity.jobApplication.jobId = jobId;

      app_entity.score = new ScoreDetails();
      app_entity.score.overallScoreLevel =
        application.additionalData.score?.softfactorsData?.resultSummary?.overallScoreLevel;
      app_entity.score.hardFactorScoreLevel =
        application.additionalData.score?.softfactorsData?.resultSummary?.hardFactorScoreLevel;
      app_entity.score.softFactorScoreLevel =
        application.additionalData.score?.softfactorsData?.resultSummary?.softFactorScoreLevel;
      app_entity.score.hasUnsatisfiedKillerRequirement =
        application.additionalData.score?.softfactorsData?.resultSummary?.hasUnsatisfiedKillerRequirement;
      app_entity.score.assessedOverallScore =
        application.additionalData.score?.softfactorsData?.resultSummary?.assessedOverallScore;
      app_entity.score.assessedHardFactorScore =
        application.additionalData.score?.softfactorsData?.resultSummary?.assessedHardFactorScore;
      app_entity.score.assessedSoftFactorScore =
        application.additionalData.score?.softfactorsData?.resultSummary?.assessedSoftFactorScore;

      app_entity.score.screeningStartedAt =
        application.additionalData.score?.softfactorsData?.timeline?.screeningStartedAt?.['$date'];
      app_entity.score.screeningSubmittedAt =
        application.additionalData.score?.softfactorsData?.timeline?.screeningSubmittedAt?.['$date'];

      app_entity.score.assessingStartedAt =
        application.additionalData.score?.softfactorsData?.timeline?.assessingStartedAt?.['$date'];
      app_entity.score.assessingSubmittedAt =
        application.additionalData.score?.softfactorsData?.timeline?.assessingSubmittedAt?.['$date'];

      app_entity.resumeData = application.additionalData.score?.sovren?.ResumeData;

      data.push(app_entity);
    }
    return data;
  }
}
